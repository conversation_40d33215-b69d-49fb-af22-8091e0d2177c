import 'package:flutter/material.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';

/// Enhanced card widget with modern styling and animations
class EnhancedCard extends StatefulWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final Gradient? gradient;
  final List<BoxShadow>? boxShadow;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool enableHover;
  final bool enableAnimation;
  final double? elevation;
  final double? width;
  final double? height;

  const EnhancedCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.gradient,
    this.boxShadow,
    this.borderRadius,
    this.border,
    this.onTap,
    this.enableHover = true,
    this.enableAnimation = true,
    this.elevation,
    this.width,
    this.height,
  });

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.stop();
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (!widget.enableHover || !widget.enableAnimation || !mounted) return;

    // Prevent setState during build
    if (mounted) {
      setState(() {
        _isHovered = isHovered;
      });

      try {
        if (isHovered) {
          _animationController.forward();
        } else {
          _animationController.reverse();
        }
      } catch (e) {
        // Animation controller might be disposed, ignore the error
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!mounted) {
      return const SizedBox.shrink();
    }

    try {
      final theme = Theme.of(context);
      final isDark = theme.brightness == Brightness.dark;

      final defaultBorderRadius = widget.borderRadius ?? AppTheme.largeRadius;
      final defaultBoxShadow =
          widget.boxShadow ??
          (isDark ? AppTheme.darkShadow : AppTheme.lightShadow);
      final defaultBackgroundColor =
          widget.backgroundColor ??
          (isDark ? AppTheme.darkSurface : AppTheme.lightSurface);

      Widget cardContent = Container(
        width: widget.width,
        height: widget.height,
        padding: widget.padding ?? const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: widget.gradient == null ? defaultBackgroundColor : null,
          gradient: widget.gradient,
          borderRadius: defaultBorderRadius,
          border: widget.border,
          boxShadow: _isHovered && widget.enableHover
              ? defaultBoxShadow
                    .map(
                      (shadow) => BoxShadow(
                        color: shadow.color,
                        blurRadius: shadow.blurRadius * 1.5,
                        offset: shadow.offset * 1.2,
                      ),
                    )
                    .toList()
              : defaultBoxShadow,
        ),
        child: widget.child,
      );

      if (widget.enableAnimation && mounted) {
        cardContent = AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            if (!mounted) return child ?? const SizedBox.shrink();
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: cardContent,
            );
          },
        );
      }

      if (widget.onTap != null) {
        cardContent = Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              if (mounted && widget.onTap != null) {
                widget.onTap!();
              }
            },
            onHover: widget.enableHover ? _onHover : null,
            borderRadius: defaultBorderRadius,
            child: cardContent,
          ),
        );
      } else if (widget.enableHover) {
        cardContent = MouseRegion(
          onEnter: (_) {
            if (mounted) _onHover(true);
          },
          onExit: (_) {
            if (mounted) _onHover(false);
          },
          child: cardContent,
        );
      }

      return Container(
        margin: widget.margin ?? const EdgeInsets.all(8),
        child: cardContent,
      );
    } catch (e) {
      // If anything goes wrong during build, return a simple container
      return Container(
        margin: widget.margin ?? const EdgeInsets.all(8),
        padding: widget.padding ?? const EdgeInsets.all(16),
        child: widget.child,
      );
    }
  }
}

/// Predefined card styles for common use cases
class CardStyles {
  static EnhancedCard primary({
    required Widget child,
    VoidCallback? onTap,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    return EnhancedCard(
      gradient: AppTheme.primaryGradient,
      onTap: onTap,
      padding: padding,
      margin: margin,
      child: child,
    );
  }

  static EnhancedCard accent({
    required Widget child,
    VoidCallback? onTap,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    return EnhancedCard(
      gradient: AppTheme.accentGradient,
      onTap: onTap,
      padding: padding,
      margin: margin,
      child: child,
    );
  }

  static EnhancedCard elevated({
    required Widget child,
    VoidCallback? onTap,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    return EnhancedCard(
      elevation: 8,
      onTap: onTap,
      padding: padding,
      margin: margin,
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.15),
          blurRadius: 20,
          offset: const Offset(0, 8),
        ),
      ],
      child: child,
    );
  }

  static EnhancedCard outlined({
    required Widget child,
    VoidCallback? onTap,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? borderColor,
  }) {
    return EnhancedCard(
      border: Border.all(
        color: borderColor ?? AppTheme.primaryGreen.withValues(alpha: 0.3),
        width: 1.5,
      ),
      onTap: onTap,
      padding: padding,
      margin: margin,
      child: child,
    );
  }
}
