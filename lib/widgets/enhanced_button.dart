import 'package:flutter/material.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';

/// Enhanced button widget with modern styling and animations
class EnhancedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;
  final ButtonStyle? style;
  final ButtonType type;
  final ButtonSize size;
  final double? width;
  final EdgeInsetsGeometry? margin;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
    this.style,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.width,
    this.margin,
  });

  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Get button style based on type
    ButtonStyle buttonStyle = _getButtonStyle(context, isDark);
    if (widget.style != null) {
      buttonStyle = buttonStyle.merge(widget.style);
    }

    Widget buttonChild = widget.isLoading
        ? SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getTextColor(isDark),
              ),
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.icon != null) ...[
                Icon(widget.icon, size: _getIconSize()),
                const SizedBox(width: 8),
              ],
              Text(
                widget.text,
                style: TextStyle(
                  fontSize: _getFontSize(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          );

    Widget button = ElevatedButton(
      onPressed: widget.isLoading ? null : widget.onPressed,
      style: buttonStyle,
      child: buttonChild,
    );

    // Add animation
    button = GestureDetector(
      onTapDown: widget.onPressed != null ? _onTapDown : null,
      onTapUp: widget.onPressed != null ? _onTapUp : null,
      onTapCancel: widget.onPressed != null ? _onTapCancel : null,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: button,
          );
        },
      ),
    );

    return Container(
      width: widget.width,
      margin: widget.margin,
      child: button,
    );
  }

  ButtonStyle _getButtonStyle(BuildContext context, bool isDark) {
    final baseStyle = ElevatedButton.styleFrom(
      padding: _getPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: AppTheme.mediumRadius,
      ),
      elevation: 2,
    );

    switch (widget.type) {
      case ButtonType.primary:
        return baseStyle.copyWith(
          backgroundColor: WidgetStateProperty.all(AppTheme.primaryGreen),
          foregroundColor: WidgetStateProperty.all(Colors.white),
        );
      case ButtonType.secondary:
        return baseStyle.copyWith(
          backgroundColor: WidgetStateProperty.all(AppTheme.secondaryGreen),
          foregroundColor: WidgetStateProperty.all(Colors.white),
        );
      case ButtonType.accent:
        return baseStyle.copyWith(
          backgroundColor: WidgetStateProperty.all(AppTheme.accentBlue),
          foregroundColor: WidgetStateProperty.all(AppTheme.lightOnSurface),
        );
      case ButtonType.outlined:
        return OutlinedButton.styleFrom(
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: AppTheme.mediumRadius,
          ),
          side: BorderSide(
            color: AppTheme.primaryGreen,
            width: 1.5,
          ),
          foregroundColor: AppTheme.primaryGreen,
        );
      case ButtonType.text:
        return TextButton.styleFrom(
          padding: _getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: AppTheme.mediumRadius,
          ),
          foregroundColor: AppTheme.primaryGreen,
        );
      case ButtonType.danger:
        return baseStyle.copyWith(
          backgroundColor: WidgetStateProperty.all(Colors.red),
          foregroundColor: WidgetStateProperty.all(Colors.white),
        );
    }
  }

  EdgeInsetsGeometry _getPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
    }
  }

  double _getFontSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 14;
      case ButtonSize.medium:
        return 16;
      case ButtonSize.large:
        return 18;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }

  Color _getTextColor(bool isDark) {
    switch (widget.type) {
      case ButtonType.primary:
      case ButtonType.secondary:
      case ButtonType.danger:
        return Colors.white;
      case ButtonType.accent:
        return AppTheme.lightOnSurface;
      case ButtonType.outlined:
      case ButtonType.text:
        return AppTheme.primaryGreen;
    }
  }
}

enum ButtonType {
  primary,
  secondary,
  accent,
  outlined,
  text,
  danger,
}

enum ButtonSize {
  small,
  medium,
  large,
}

/// Predefined button styles for common use cases
class ButtonStyles {
  static EnhancedButton primary({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    ButtonSize size = ButtonSize.medium,
    double? width,
  }) {
    return EnhancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      type: ButtonType.primary,
      size: size,
      width: width,
    );
  }

  static EnhancedButton secondary({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    ButtonSize size = ButtonSize.medium,
    double? width,
  }) {
    return EnhancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      type: ButtonType.secondary,
      size: size,
      width: width,
    );
  }

  static EnhancedButton outlined({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    ButtonSize size = ButtonSize.medium,
    double? width,
  }) {
    return EnhancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      type: ButtonType.outlined,
      size: size,
      width: width,
    );
  }

  static EnhancedButton danger({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    ButtonSize size = ButtonSize.medium,
    double? width,
  }) {
    return EnhancedButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      isLoading: isLoading,
      type: ButtonType.danger,
      size: size,
      width: width,
    );
  }
}
