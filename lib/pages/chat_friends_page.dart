import 'dart:io';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diogeneschatbot/features/rtc/presentation/pages/connect.dart';
import 'package:diogeneschatbot/models/chat_room.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/group_chat_details_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/tool/image_picker.dart';
import 'package:diogeneschatbot/tool/image_picker_stub.dart'
    if (dart.library.io) 'package:diogeneschatbot/tool/image_picker_io.dart'
    if (dart.library.html) 'package:diogeneschatbot/tool/image_picker_web.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/util/util_file.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/audio_recorder_widget.dart';
import 'package:diogeneschatbot/widgets/chat_friends_message_item_widget.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

enum ChatRoomUsageType { SendToUser, SendToChatroom, NormalMessage }

class ChatFriendsPage extends StatefulWidget {
  final String chatRoomId;
  final List<String> memberIds;

  ChatFriendsPage({required this.chatRoomId, required this.memberIds});

  @override
  _ChatFriendsPageState createState() => _ChatFriendsPageState();
}

class _ChatFriendsPageState extends State<ChatFriendsPage> {
  final ChatRepository _chatRepository = ChatRepository();
  TextEditingController _messageController = TextEditingController();
  FirebaseFirestore _firestore = FirebaseFirestore.instance;
  ProfileRepository _profileRepository = ProfileRepository();
  ChatRoomUsageType _usageType = ChatRoomUsageType.NormalMessage;
  final ScrollController _scrollController = ScrollController();
  CommonImagePicker _imageData = CommonImagePicker();

  // Determine if the platform is desktop
  bool isDesktop =
      !kIsWeb && (Platform.isLinux || Platform.isMacOS || Platform.isWindows);
  String user_name = FirebaseAuth.instance.currentUser!.displayName ?? "";

  List<String> _chatRoomMembers = [];
  List<Map<String, String>> _localChatHistory = [];
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    // After initialization, request notification permissions if not requested
    _checkNotificationPermission();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _checkNotificationPermission() async {
    final messaging = FirebaseMessaging.instance;
    final settings = await messaging.getNotificationSettings();

    if (settings.authorizationStatus == AuthorizationStatus.notDetermined) {
      // Permission not determined, request it
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _requestNotificationPermissions();
      });
    } else if (settings.authorizationStatus == AuthorizationStatus.denied) {
      // Permission denied, show reminder
      _showPermissionReminder();
    }
  }

  Future<void> _requestNotificationPermissions() async {
    final messaging = FirebaseMessaging.instance;
    final settings = await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      logger.d('User granted permission.');
    } else {
      // Show a dialog or banner reminding users to enable notifications
      _showPermissionReminder();
    }
  }

  void _showPermissionReminder() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Permissions'),
          content: const Text(
            'Please enable notification permissions for a better experience. You can close the app, enable notifications in system settings. Then open it again.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Okay'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleSendMessage(
    String messageText,
    Usage usage, {
    ChatMessageType messageType = ChatMessageType.text,
  }) async {
    setState(() {
      _isSending = true;
    });

    String? mentionedRecipient = _getMentionedRecipient(messageText);
    ChatRoomUsageType usageType = _usageType;

    if (mentionedRecipient != null &&
        mentionedRecipient.toLowerCase() == '(ai)') {
      usageType = ChatRoomUsageType.SendToChatroom;
    }

    switch (usageType) {
      case ChatRoomUsageType.SendToUser:
        await _sendToUser(messageText, usage);
        break;

      case ChatRoomUsageType.SendToChatroom:
        await _sendToChatroom(messageText, usage);
        break;

      case ChatRoomUsageType.NormalMessage:
      default:
        await _sendMessage(messageText, messageType: messageType);
        break;
    }
    setState(() {
      _isSending = false;
    });
  }

  Future<void> _sendToUser(String messageText, Usage usage) async {
    List<Map<String, String>> lastMessages = _getLastLocalChatroomMessages(20);
    String aiResponse = await Util.CallChatAPI(
      messageText,
      usage,
      lastMessages,
      FirebaseAuth.instance.currentUser!.uid,
      widget.chatRoomId,
    );
    _showAiPopup(aiResponse);
  }

  Future<void> _sendToChatroom(String messageText, Usage usage) async {
    List<Map<String, String>> lastMessages = _getLastLocalChatroomMessages(20);
    String aiResponse = await Util.CallChatAPI(
      messageText,
      usage,
      lastMessages,
      FirebaseAuth.instance.currentUser!.uid,
      widget.chatRoomId,
      lastQuestionsToIncludeCount: 2,
    );
    await _sendMessage(messageText);
    await _sendMessage(aiResponse, isFromAi: true);
  }

  Future<void> _sendMessage(
    String content, {
    bool isFromAi = false,
    messageType = ChatMessageType.text,
  }) async {
    if (content.trim().isEmpty) return;

    String senderId = FirebaseAuth.instance.currentUser!.uid;

    Message message = Message(
      messageId: _firestore.collection('messages').doc().id,
      chatRoomId: widget.chatRoomId,
      senderId: senderId,
      content: content,
      timestamp: DateTime.now().toUtc(),
      messageType: messageType,
      isFromAi: isFromAi,
    );

    await _chatRepository.saveMessage(message);
    _messageController.clear();
  }

  String? _getMentionedRecipient(String messageText) {
    if (messageText.startsWith('@')) {
      int endIdx = messageText.indexOf(' ');
      if (endIdx > 0) {
        return messageText.substring(1, endIdx);
      }
    }
    return null;
  }

  Future<void> _showAiPopup(String aiResponse) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("AI Response"),
          content: SelectableText(aiResponse),
          actions: [
            TextButton(
              onPressed: () {
                /* Add copy/share code here */
              },
              child: Text('Copy/Share'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close'),
            ),
          ],
        );
      },
    );
  }

  List<Map<String, String>> _getLastLocalChatroomMessages(int maxMessages) {
    int startIndex = max(0, _localChatHistory.length - maxMessages);
    return _localChatHistory.sublist(startIndex);
  }

  void _handleTranscription(String transcript) {
    logger.d('Transcribed text: $transcript');
    setState(() {
      _messageController.text = transcript;
    });
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Usage chat = Usage(
      onscreenMessage: localizations.chatWithAiBot,
      type: UsageType.chat,
      icon: Icons.chat,
      imagePath: "assets/page_icons/chat_with_ai_bot.png",
      systemPromptMessage: 'You are a helpful assistant in this chat.',
    );

    return Scaffold(
      backgroundColor: isDark
          ? AppTheme.darkBackground
          : AppTheme.lightBackground,
      body: Container(
        decoration: BoxDecoration(
          gradient: isDark ? AppTheme.darkGradient : AppTheme.lightGradient,
        ),
        child: SafeArea(
          child: FutureBuilder<ChatRoom?>(
            future: _chatRepository.getChatRoom(widget.chatRoomId),
            builder:
                (
                  BuildContext context,
                  AsyncSnapshot<ChatRoom?> chatRoomSnapshot,
                ) {
                  if (chatRoomSnapshot.connectionState ==
                      ConnectionState.waiting) {
                    return _buildLoadingState();
                  }

                  if (!chatRoomSnapshot.hasData ||
                      chatRoomSnapshot.data == null) {
                    return _buildErrorState('Chat room not found.');
                  }

                  ChatRoom chatRoom = chatRoomSnapshot.data!;
                  String currentUserId = FirebaseAuth.instance.currentUser!.uid;

                  return FutureBuilder<List<String>>(
                    future: getMemberNames(),
                    builder:
                        (
                          BuildContext context,
                          AsyncSnapshot<List<String>> snapshot,
                        ) {
                          var memberNames = snapshot.data;

                          return Column(
                            children: [
                              // Enhanced App Bar
                              _buildEnhancedAppBar(
                                memberNames,
                                chatRoom,
                                currentUserId,
                                theme,
                                isDark,
                              ),

                              // Chat Messages Area
                              Expanded(child: _buildChatArea()),

                              // Message Input Area
                              _buildMessageInputArea(chat, theme, isDark),
                            ],
                          );
                        },
                  );
                },
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading chat...',
            style: TextStyle(
              color: AppTheme.primaryGreen,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.withValues(alpha: 0.7),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Go Back'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedAppBar(
    List<String>? memberNames,
    ChatRoom chatRoom,
    String currentUserId,
    ThemeData theme,
    bool isDark,
  ) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              memberNames == null ? 'Loading...' : memberNames.join(', '),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${chatRoom.memberIds.length} members',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.groups, color: Colors.white),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => GroupChatDetailsPage(
                      chatRoom: chatRoom,
                      currentUserId: currentUserId,
                    ),
                  ),
                );
              },
              tooltip: 'Group details',
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.video_call, color: Colors.white),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ConnectPage(
                      name: user_name,
                      chatRoomId: widget.chatRoomId,
                    ),
                  ),
                );
              },
              tooltip: 'Start video call',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatArea() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: StreamBuilder<QuerySnapshot>(
        stream: _chatRepository.getChatHistory(widget.chatRoomId),
        builder: (BuildContext context, AsyncSnapshot<QuerySnapshot> snapshot) {
          if (snapshot.hasError) {
            return _buildChatErrorState(snapshot.error.toString());
          }

          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildChatLoadingState();
          }

          if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
            return _buildEmptyChatState();
          }

          return ListView.builder(
            reverse: true,
            controller: _scrollController,
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: snapshot.data!.docs.length,
            itemBuilder: (context, index) {
              return ChatFriendsMessageItemWidget(
                profileRepository: _profileRepository,
                localChatHistory: _localChatHistory,
                snapshot: snapshot,
                index: index,
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildChatLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryGreen),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading messages...',
            style: TextStyle(color: AppTheme.primaryGreen, fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildChatErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red.withValues(alpha: 0.7),
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading messages',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyChatState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: AppTheme.primaryGreen.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No messages yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start the conversation by sending a message!',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInputArea(Usage chat, ThemeData theme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            // Voice recording button
            if (kIsWeb || (!kIsWeb && !(Platform.isMacOS && kDebugMode)))
              Container(
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: AudioRecorderWidget(
                  onTranscriptionComplete: _handleTranscription,
                ),
              ),

            // Message input field
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: isDark
                      ? AppTheme.darkSurfaceVariant
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _messageController,
                  maxLines: null,
                  textCapitalization: TextCapitalization.sentences,
                  decoration: InputDecoration(
                    hintText: 'Type your message...',
                    hintStyle: TextStyle(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                  style: theme.textTheme.bodyMedium,
                  onSubmitted: (text) async {
                    if (text.trim().isNotEmpty) {
                      await _handleSendMessage(text, chat);
                      _messageController.clear();
                    }
                  },
                ),
              ),
            ),

            const SizedBox(width: 8),

            // Media picker button
            Container(
              decoration: BoxDecoration(
                color: AppTheme.accentBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: _isSending
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.accentBlue,
                          ),
                        ),
                      )
                    : Icon(Icons.attach_file, color: AppTheme.accentBlue),
                onPressed: _isSending
                    ? null
                    : () => _showEnhancedImagePicker(chat),
                tooltip: 'Attach media',
              ),
            ),

            const SizedBox(width: 8),

            // Send button
            Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: _isSending
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Icon(Icons.send_rounded, color: Colors.white),
                onPressed: _isSending
                    ? null
                    : () async {
                        final text = _messageController.text.trim();
                        if (text.isNotEmpty) {
                          await _handleSendMessage(text, chat);
                          _messageController.clear();
                        }
                      },
                tooltip: 'Send message',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showEnhancedImagePicker(Usage chat) async {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Title
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 8,
                ),
                child: Text(
                  'Choose Media',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Options
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildMediaOption(
                      context,
                      icon: Icons.photo_library_rounded,
                      label: 'Gallery',
                      color: AppTheme.accentBlue,
                      onTap: () => _pickMedia(chat, false),
                    ),
                    if (!isDesktop)
                      _buildMediaOption(
                        context,
                        icon: Icons.camera_alt_rounded,
                        label: 'Camera',
                        color: AppTheme.accentCoral,
                        onTap: () => _pickMedia(chat, true),
                      ),
                  ],
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMediaOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickMedia(Usage chat, bool useCamera) async {
    Navigator.pop(context);
    setState(() {
      _isSending = true;
    });

    try {
      List<CommonImageData> pickedImages = [];
      if (useCamera) {
        pickedImages = await _imageData.pickImages(
          context: context,
          allowCamera: true,
        );
      } else {
        pickedImages = await _imageData.pickImages(context: context);
      }

      if (pickedImages.isNotEmpty) {
        List<String> urls = await _imageData.uploadImages(
          pickedImages,
          userId: FirebaseAuth.instance.currentUser!.uid,
        );

        for (String url in urls) {
          ChatMessageType messageType = Util_File.getFileType(
            Util_File.getFileExtension(url) ?? "",
          );
          await _handleSendMessage(url, chat, messageType: messageType);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking media: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  Future<List<String>> getMemberNames() async {
    List<String> names = [];
    List<Profile?> memberProfiles = await _profileRepository.getProfiles(
      widget.memberIds,
    );
    for (Profile? memberProfile in memberProfiles) {
      if (memberProfile != null) {
        if (memberProfile.id == FirebaseAuth.instance.currentUser!.uid) {
          // setState(() {
          user_name = memberProfile.name;
          // });
        } else {
          // Do not add current user name
          names.add(memberProfile.name);
        }
      }
    }
    return names;
  }
}
