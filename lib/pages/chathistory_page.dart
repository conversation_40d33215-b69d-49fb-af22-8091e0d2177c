import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:diogeneschatbot/models/apiusagemodel.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/create_post_page.dart';
import 'package:diogeneschatbot/services/apiusage_service.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/tts_button.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_animate/flutter_animate.dart';
import 'package:readmore/readmore.dart';
import 'package:grouped_list/grouped_list.dart';

class ChatHistoryPage extends StatefulWidget {
  final Usage usage;

  const ChatHistoryPage({super.key, required this.usage});

  @override
  _ChatHistoryPageState createState() => _ChatHistoryPageState(usage);
}

class _ChatHistoryPageState extends State<ChatHistoryPage> {
  List<ApiUsage> _chatHistory = [];
  late Usage usage;

  String userUUID = Util.getUserID();

  _ChatHistoryPageState(this.usage);

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
  }

  Future<void> _loadChatHistory() async {
    List<ApiUsage> chatHistoryString = await ApiUsageService()
        .getApiUsageForUserAndType(userUUID, usage.type.toString());

    setState(() {
      _chatHistory = chatHistoryString;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Chat History')),
      body: GroupedListView<ApiUsage, DateTime>(
        elements: _chatHistory,
        groupBy: (ApiUsage chatMessage) => DateTime(
          chatMessage.date.year,
          chatMessage.date.month,
          chatMessage.date.day,
        ),
        groupComparator: (DateTime date1, DateTime date2) =>
            date2.compareTo(date1),
        itemComparator: (ApiUsage chat1, ApiUsage chat2) =>
            chat2.date.compareTo(chat1.date),
        order: GroupedListOrder.ASC,
        groupSeparatorBuilder: (DateTime groupByValue) => Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            '${groupByValue.day}/${groupByValue.month}/${groupByValue.year}',
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
        itemBuilder: (context, ApiUsage chatMessage) {
          return Column(
            children: [
              Row(
                children: [
                  Text(
                    chatMessage.date.toLocal().toString().substring(0, 19),
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.only(right: 40),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.blue, width: 1),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                        child: ReadMoreText(
                          chatMessage.question!.trim(),
                          trimLines: 2,
                          preDataText: "Question:",
                          preDataTextStyle: TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                          style: TextStyle(color: Colors.blue, fontSize: 12),
                          colorClickableText: Colors.pink,
                          trimMode: TrimMode.Line,
                          trimCollapsedText: '...Show more',
                          trimExpandedText: ' show less',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  // Displaying all images if available
                  if (chatMessage.imageUrls != null &&
                      chatMessage.imageUrls!.isNotEmpty)
                    Wrap(
                      spacing: 8.0, // Add space between images
                      runSpacing: 8.0, // Add space between rows
                      children: chatMessage.imageUrls!
                          .map(
                            (imageUrl) => Container(
                              width: 100, // Set a fixed width
                              height: 100, // Set a fixed height
                              child: CachedNetworkImage(
                                imageUrl: imageUrl,
                                fit: BoxFit
                                    .cover, // Ensure the image scales properly
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  chatMessage.audioUrls != null &&
                          chatMessage.audioUrls!.isNotEmpty
                      ? Text("Audio available")
                      : SizedBox(),
                  chatMessage.textUrls != null &&
                          chatMessage.textUrls!.isNotEmpty
                      ? Text("Text available")
                      : SizedBox(),
                  chatMessage.videoUrls != null &&
                          chatMessage.videoUrls!.isNotEmpty
                      ? Text("Video available")
                      : SizedBox(),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.only(left: 40),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.green, width: 1),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                        child:
                            usage.type.toString() !=
                                UsageType.generateImage.toString()
                            ? ReadMoreText(
                                chatMessage.answer!.trim(),
                                trimLines: 2,
                                preDataText: "Answer:",
                                preDataTextStyle: TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                                style: TextStyle(
                                  color: Colors.green,
                                  fontSize: 12,
                                ),
                                colorClickableText: Colors.pink,
                                trimMode: TrimMode.Line,
                                trimCollapsedText: '...Show more',
                                trimExpandedText: ' show less',
                              )
                            : CachedNetworkImage(
                                imageUrl: chatMessage.answer!.trim(),
                                fit: BoxFit.contain,
                              ),
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  TTSButton(textToSpeak: chatMessage.answer!),
                  IconButton(
                    icon: Icon(Icons.copy),
                    onPressed: () {
                      Clipboard.setData(
                        ClipboardData(text: chatMessage.answer!),
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text("Copied to clipboard"),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    },
                  ),
                  if (kIsWeb || (!Platform.isIOS && !Platform.isMacOS))
                    IconButton(
                      icon: Icon(Icons.share),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CreatePostPage(
                              userId: userUUID,
                              initialText:
                                  usage.type.toString() !=
                                      UsageType.generateImage.toString()
                                  ? 'question: ${chatMessage.question!} answer: ${chatMessage.answer!}'
                                  : "question: ${chatMessage.question!}",
                              initialMedialUrls:
                                  usage.type.toString() !=
                                      UsageType.generateImage.toString()
                                  ? []
                                  : [chatMessage.answer!],
                            ),
                          ),
                        );
                      },
                    ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }
}
