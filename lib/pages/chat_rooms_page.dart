import 'package:diogeneschatbot/models/chat_room.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/pages/chat_friends_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_animate/flutter_animate.dart';
import 'package:uuid/uuid.dart';

class ChatRoomsPage extends StatefulWidget {
  final String currentUserId;

  ChatRoomsPage({required this.currentUserId});

  @override
  _ChatRoomsPageState createState() => _ChatRoomsPageState();
}

class _ChatRoomsPageState extends State<ChatRoomsPage> {
  final ChatRepository _chatRepository = ChatRepository();
  final _profileRepository = ProfileRepository();

  // Add a map to cache member names to avoid repeated API calls
  final Map<String, String> _memberNamesCache = {};

  // Add debounce mechanism to prevent rapid navigation
  DateTime? _lastNavigationTime;

  @override
  void dispose() {
    // Clear cache on dispose
    _memberNamesCache.clear();
    super.dispose();
  }

  Future<String> getMemberNames(List<String> memberIds) async {
    if (!mounted) return '';

    // Create a cache key from member IDs
    final cacheKey = memberIds.join(',');
    if (_memberNamesCache.containsKey(cacheKey)) {
      return _memberNamesCache[cacheKey]!;
    }

    try {
      List<String> names = [];
      List<Profile?> memberProfiles = await _profileRepository.getProfiles(
        memberIds,
      );

      if (!mounted) return '';

      for (Profile? memberProfile in memberProfiles) {
        if (memberProfile != null) {
          if (memberProfile.id != FirebaseAuth.instance.currentUser!.uid) {
            // Do not add current user name
            names.add(memberProfile.name);
          }
        }
      }

      final result = names.join(', ');
      _memberNamesCache[cacheKey] = result;
      return result;
    } catch (e) {
      if (mounted) {
        // Log error but don't crash the UI
        print('Error getting member names: $e');
      }
      return 'Group Chat';
    }
  }

  void _navigateToChatRoom(ChatRoom chatRoom) {
    final now = DateTime.now();

    // Debounce navigation to prevent rapid taps
    if (_lastNavigationTime != null &&
        now.difference(_lastNavigationTime!).inMilliseconds < 1000) {
      return;
    }

    _lastNavigationTime = now;

    if (mounted && context.mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => ChatFriendsPage(
            chatRoomId: chatRoom.chatRoomId,
            memberIds: chatRoom.memberIds,
          ),
        ),
      );
    }
  }

  Future<void> _createNewChatRoom() async {
    if (!mounted) return;

    try {
      // Create the chat room if it doesn't exist
      ChatRoom chatRoom = ChatRoom(
        chatRoomId: "groupchat_${Uuid().v4()}",
        memberIds: [widget.currentUserId],
        groupAdminId: widget.currentUserId,
      );
      await _chatRepository.createChatRoom(chatRoom);

      // Navigate to the chat screen using the debounced method
      _navigateToChatRoom(chatRoom);
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error creating chat room: $e')));
      }
    }
  }

  void _showDeleteDialog(ChatRoom chatRoom) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              const SizedBox(width: 8),
              const Text('Delete Chat Room'),
            ],
          ),
          content: const Text(
            'Are you sure you want to delete this chat room? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
              onPressed: () async {
                // Store context reference before async operation
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                try {
                  await _chatRepository.deleteGroupChat(chatRoom.chatRoomId);
                  if (mounted && context.mounted) {
                    navigator.pop();
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(
                        content: Text('Chat room deleted successfully'),
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted && context.mounted) {
                    navigator.pop();
                    scaffoldMessenger.showSnackBar(
                      SnackBar(content: Text('Error deleting chat room: $e')),
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark
          ? AppTheme.darkBackground
          : AppTheme.lightBackground,
      appBar: AppBarStyles.primary(
        title: 'Chat Rooms',
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _createNewChatRoom,
              icon: const Icon(Icons.add_rounded, color: Colors.white),
              tooltip: 'Create new chat room',
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: isDark ? AppTheme.darkGradient : AppTheme.lightGradient,
        ),
        child: StreamBuilder<List<ChatRoom>>(
          stream: _chatRepository.getUserChatRooms(widget.currentUserId),
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: theme.colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading chat rooms',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${snapshot.error}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.primaryGreen,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Loading your chat rooms...',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            final chatRooms = snapshot.data!;

            if (chatRooms.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 80,
                      color: AppTheme.primaryGreen.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'No Chat Rooms Yet',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: theme.colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Create your first chat room to start conversations',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: _createNewChatRoom,
                      icon: const Icon(Icons.add_rounded),
                      label: const Text('Create Chat Room'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryGreen,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              key: const ValueKey('chat_rooms_list'),
              padding: const EdgeInsets.all(16),
              itemCount: chatRooms.length,
              itemBuilder: (context, index) {
                final chatRoom = chatRooms[index];
                return FutureBuilder<String>(
                  key: ValueKey('member_names_${chatRoom.chatRoomId}'),
                  future: getMemberNames(chatRoom.memberIds),
                  builder: (BuildContext context, AsyncSnapshot<String> nameSnapshot) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: EnhancedCard(
                        key: ValueKey('chat_room_card_${chatRoom.chatRoomId}'),
                        onTap: () => _navigateToChatRoom(chatRoom),
                        child: Row(
                          children: [
                            // Chat room avatar
                            Container(
                              width: 56,
                              height: 56,
                              decoration: BoxDecoration(
                                gradient: AppTheme.primaryGradient,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.group_rounded,
                                color: Colors.white,
                                size: 28,
                              ),
                            ),
                            const SizedBox(width: 16),
                            // Chat room info
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    nameSnapshot.data?.isNotEmpty == true
                                        ? "${nameSnapshot.data} (${chatRoom.memberIds.length})"
                                        : 'Group Chat (${chatRoom.memberIds.length})',
                                    style: theme.textTheme.titleMedium
                                        ?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: theme.colorScheme.onSurface,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Tap to enter chat',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurface
                                          .withValues(alpha: 0.6),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Admin actions
                            if (widget.currentUserId == chatRoom.groupAdminId)
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.red.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: Icon(
                                    Icons.delete_outline_rounded,
                                    color: Colors.red,
                                  ),
                                  onPressed: () => _showDeleteDialog(chatRoom),
                                  tooltip: 'Delete chat room',
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }
}
